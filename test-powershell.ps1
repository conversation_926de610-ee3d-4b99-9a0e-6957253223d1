# PowerShell脚本语法测试
# 用于验证start-mldev.ps1的语法正确性

Write-Host "测试PowerShell脚本语法..." -ForegroundColor Cyan

# 测试脚本加载
try {
    Write-Host "1. 测试脚本加载..." -ForegroundColor Yellow
    $scriptContent = Get-Content "start-mldev.ps1" -Raw
    $null = [System.Management.Automation.PSParser]::Tokenize($scriptContent, [ref]$null)
    Write-Host "✓ 脚本语法检查通过" -ForegroundColor Green
}
catch {
    Write-Host "✗ 脚本语法错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试函数定义
try {
    Write-Host "2. 测试函数定义..." -ForegroundColor Yellow
    . .\start-mldev.ps1
    
    # 检查关键函数是否存在
    $functions = @(
        "Write-Success",
        "Write-Error", 
        "Write-Warning",
        "Write-Info",
        "Show-Header",
        "Test-DockerEnvironment",
        "Set-Environment",
        "Show-EnvironmentInfo",
        "Start-Environment",
        "Stop-Environment",
        "Rebuild-Environment",
        "Show-Status",
        "Enter-Shell",
        "Start-JupyterLab",
        "Show-Logs",
        "Clear-Environment",
        "Show-Menu",
        "Main"
    )
    
    foreach ($func in $functions) {
        if (Get-Command $func -ErrorAction SilentlyContinue) {
            Write-Host "  ✓ 函数 $func 定义正确" -ForegroundColor Green
        } else {
            Write-Host "  ✗ 函数 $func 未找到" -ForegroundColor Red
        }
    }
}
catch {
    Write-Host "✗ 函数定义错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 测试参数处理
try {
    Write-Host "3. 测试参数处理..." -ForegroundColor Yellow
    
    # 测试不同的Action参数
    $testActions = @("build", "start", "stop", "status", "shell", "jupyter", "logs", "clean")
    
    foreach ($action in $testActions) {
        Write-Host "  测试Action: $action" -ForegroundColor Gray
        # 这里只是验证参数能被正确解析，不实际执行
    }
    
    Write-Host "✓ 参数处理测试通过" -ForegroundColor Green
}
catch {
    Write-Host "✗ 参数处理错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n所有测试通过！PowerShell脚本语法正确。" -ForegroundColor Green
Write-Host "现在可以安全运行: .\start-mldev.ps1" -ForegroundColor Cyan
