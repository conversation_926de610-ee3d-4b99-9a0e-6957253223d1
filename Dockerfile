# 多语言机器学习开发环境 Dockerfile
# 基于NVIDIA CUDA镜像，支持Python、C++、Go和CUDA开发
# 适配硬件：Intel 13600KF + RTX 4070 Super + 64GB RAM

# 使用NVIDIA官方CUDA镜像作为基础镜像
FROM nvidia/cuda:11.8-devel-ubuntu22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 设置工作目录
WORKDIR /workspace

# 更新系统并安装基础工具
RUN apt-get update && apt-get install -y \
    # 基础开发工具
    build-essential \
    cmake \
    git \
    curl \
    wget \
    vim \
    nano \
    htop \
    tree \
    unzip \
    software-properties-common \
    # 网络工具
    net-tools \
    iputils-ping \
    # SSL证书
    ca-certificates \
    # Python开发依赖
    python3-dev \
    python3-pip \
    # C++开发库
    libboost-all-dev \
    libeigen3-dev \
    libopencv-dev \
    # 系统库
    libssl-dev \
    libffi-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libncurses5-dev \
    libncursesw5-dev \
    xz-utils \
    tk-dev \
    libxml2-dev \
    libxmlsec1-dev \
    libffi-dev \
    liblzma-dev \
    # zsh相关
    zsh \
    fonts-powerline \
    && rm -rf /var/lib/apt/lists/*

# 安装miniConda
ENV CONDA_DIR=/opt/conda
ENV PATH=$CONDA_DIR/bin:$PATH
RUN wget --quiet https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O ~/miniconda.sh && \
    /bin/bash ~/miniconda.sh -b -p $CONDA_DIR && \
    rm ~/miniconda.sh && \
    conda clean -tipsy && \
    ln -s $CONDA_DIR/etc/profile.d/conda.sh /etc/profile.d/conda.sh && \
    echo ". $CONDA_DIR/etc/profile.d/conda.sh" >> ~/.bashrc && \
    echo "conda activate base" >> ~/.bashrc

# 创建Python环境并安装机器学习库
RUN conda create -n mldev python=3.10 -y && \
    echo "conda activate mldev" >> ~/.bashrc
    
# 激活环境并安装Python包
SHELL ["conda", "run", "-n", "mldev", "/bin/bash", "-c"]
RUN pip install --no-cache-dir \
    # PyTorch生态系统（CUDA 11.8版本）
    torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 \
    # 科学计算库
    numpy pandas matplotlib seaborn plotly \
    # 机器学习库
    scikit-learn xgboost lightgbm \
    # 深度学习工具
    transformers datasets accelerate \
    # Jupyter环境
    jupyter jupyterlab ipywidgets \
    # 数据处理
    pillow opencv-python \
    # 开发工具
    black flake8 pytest mypy \
    # 其他常用库
    requests beautifulsoup4 lxml \
    tqdm rich typer click \
    pydantic fastapi uvicorn

# 安装Go语言
ENV GO_VERSION=1.21.5
ENV GOROOT=/usr/local/go
ENV GOPATH=/workspace/go
ENV PATH=$GOROOT/bin:$GOPATH/bin:$PATH
RUN wget https://golang.org/dl/go${GO_VERSION}.linux-amd64.tar.gz && \
    tar -C /usr/local -xzf go${GO_VERSION}.linux-amd64.tar.gz && \
    rm go${GO_VERSION}.linux-amd64.tar.gz && \
    mkdir -p $GOPATH/src $GOPATH/bin $GOPATH/pkg

# 安装Go开发工具
RUN go install golang.org/x/tools/gopls@latest && \
    go install github.com/go-delve/delve/cmd/dlv@latest && \
    go install golang.org/x/tools/cmd/goimports@latest && \
    go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 配置CUDA环境变量
ENV CUDA_HOME=/usr/local/cuda
ENV CUDA_ROOT=/usr/local/cuda
ENV LD_LIBRARY_PATH=$CUDA_HOME/lib64:$LD_LIBRARY_PATH
ENV PATH=$CUDA_HOME/bin:$PATH

# 安装cuDNN（如果需要）
# 注意：这里需要根据实际的cuDNN版本进行调整
RUN apt-get update && apt-get install -y \
    libcudnn8 \
    libcudnn8-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装oh-my-zsh
RUN sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended && \
    chsh -s $(which zsh)

# 配置zsh主题和插件
RUN sed -i 's/ZSH_THEME="robbyrussell"/ZSH_THEME="agnoster"/' ~/.zshrc && \
    echo 'plugins=(git python golang docker kubectl)' >> ~/.zshrc && \
    echo 'export PATH=$PATH:/usr/local/go/bin:/workspace/go/bin' >> ~/.zshrc && \
    echo 'export GOPATH=/workspace/go' >> ~/.zshrc && \
    echo 'export GOROOT=/usr/local/go' >> ~/.zshrc && \
    echo 'source /opt/conda/etc/profile.d/conda.sh' >> ~/.zshrc && \
    echo 'conda activate mldev' >> ~/.zshrc

# 创建开发目录结构
RUN mkdir -p /workspace/{python,cpp,go,cuda,notebooks,data,models}

# 设置权限
RUN chmod -R 755 /workspace

# 暴露端口
EXPOSE 8888 8000 3000 8080 6006

# 设置默认shell为zsh
ENV SHELL=/bin/zsh

# 启动命令
CMD ["/bin/zsh"]
