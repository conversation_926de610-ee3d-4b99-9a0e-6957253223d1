#!/bin/bash

# 快速环境验证脚本
# 用于在容器启动后快速检查各组件是否正常工作

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 输出函数
print_header() {
    echo -e "${MAGENTA}========================================${NC}"
    echo -e "${MAGENTA}  $1${NC}"
    echo -e "${MAGENTA}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ $1${NC}"
}

# 检查函数
check_command() {
    local cmd=$1
    local name=$2
    
    if command -v "$cmd" >/dev/null 2>&1; then
        local version=$($cmd --version 2>/dev/null | head -n1 || echo "版本信息不可用")
        print_success "$name 可用: $version"
        return 0
    else
        print_error "$name 不可用"
        return 1
    fi
}

check_python_package() {
    local package=$1
    
    if python3 -c "import $package" 2>/dev/null; then
        local version=$(python3 -c "import $package; print(getattr($package, '__version__', '版本未知'))" 2>/dev/null)
        print_success "$package 已安装: $version"
        return 0
    else
        print_error "$package 未安装"
        return 1
    fi
}

# 主要检查流程
main() {
    print_header "快速环境验证"
    
    # 系统信息
    print_info "主机名: $(hostname)"
    print_info "操作系统: $(uname -s) $(uname -r)"
    print_info "架构: $(uname -m)"
    print_info "当前用户: $(whoami)"
    print_info "工作目录: $(pwd)"
    echo
    
    # 基础工具检查
    print_header "基础工具检查"
    check_command "git" "Git"
    check_command "curl" "cURL"
    check_command "wget" "wget"
    check_command "vim" "Vim"
    check_command "zsh" "Zsh"
    echo
    
    # Python环境检查
    print_header "Python环境检查"
    if check_command "python3" "Python3"; then
        print_info "Python路径: $(which python3)"
        
        # 检查conda
        if command -v conda >/dev/null 2>&1; then
            print_success "Conda 可用: $(conda --version)"
            print_info "当前环境: $CONDA_DEFAULT_ENV"
        else
            print_warning "Conda 不可用"
        fi
        
        # 检查重要的Python包
        echo
        print_info "检查Python包..."
        check_python_package "numpy"
        check_python_package "pandas"
        check_python_package "torch"
        check_python_package "jupyter"
    fi
    echo
    
    # PyTorch和CUDA检查
    print_header "PyTorch和CUDA检查"
    if python3 -c "import torch" 2>/dev/null; then
        local torch_version=$(python3 -c "import torch; print(torch.__version__)")
        print_success "PyTorch版本: $torch_version"
        
        local cuda_available=$(python3 -c "import torch; print(torch.cuda.is_available())")
        if [ "$cuda_available" = "True" ]; then
            local gpu_count=$(python3 -c "import torch; print(torch.cuda.device_count())")
            local gpu_name=$(python3 -c "import torch; print(torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'N/A')")
            print_success "CUDA 可用"
            print_info "GPU数量: $gpu_count"
            print_info "GPU名称: $gpu_name"
        else
            print_warning "CUDA 不可用"
        fi
    else
        print_error "PyTorch 未安装"
    fi
    echo
    
    # Go环境检查
    print_header "Go环境检查"
    if check_command "go" "Go"; then
        print_info "GOROOT: ${GOROOT:-未设置}"
        print_info "GOPATH: ${GOPATH:-未设置}"
        
        # 检查Go工具
        echo
        print_info "检查Go工具..."
        check_command "gofmt" "gofmt"
        check_command "goimports" "goimports"
        check_command "gopls" "gopls"
    fi
    echo
    
    # C++环境检查
    print_header "C++环境检查"
    check_command "gcc" "GCC"
    check_command "g++" "G++"
    check_command "cmake" "CMake"
    check_command "make" "Make"
    echo
    
    # CUDA工具检查
    print_header "CUDA工具检查"
    check_command "nvcc" "NVCC"
    if command -v nvidia-smi >/dev/null 2>&1; then
        print_success "nvidia-smi 可用"
        print_info "GPU信息:"
        nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader,nounits | while read line; do
            print_info "  $line"
        done
    else
        print_error "nvidia-smi 不可用"
    fi
    echo
    
    # 网络检查
    print_header "网络连接检查"
    if ping -c 1 google.com >/dev/null 2>&1; then
        print_success "外网连接正常"
    else
        print_warning "外网连接异常"
    fi
    
    if ping -c 1 github.com >/dev/null 2>&1; then
        print_success "GitHub连接正常"
    else
        print_warning "GitHub连接异常"
    fi
    echo
    
    # 端口检查
    print_header "端口检查"
    local ports=(8888 8000 3000 8080 6006)
    for port in "${ports[@]}"; do
        if netstat -ln 2>/dev/null | grep ":$port " >/dev/null; then
            print_info "端口 $port 正在使用"
        else
            print_info "端口 $port 可用"
        fi
    done
    echo
    
    # 目录结构检查
    print_header "目录结构检查"
    local dirs=("python" "cpp" "go" "cuda" "notebooks" "data" "models")
    for dir in "${dirs[@]}"; do
        if [ -d "/workspace/$dir" ]; then
            print_success "目录 /workspace/$dir 存在"
        else
            print_warning "目录 /workspace/$dir 不存在"
            mkdir -p "/workspace/$dir" 2>/dev/null && print_info "已创建目录 /workspace/$dir"
        fi
    done
    echo
    
    # 简单性能测试
    print_header "简单性能测试"
    print_info "运行CPU测试..."
    local cpu_start=$(date +%s%N)
    python3 -c "
import time
start = time.time()
sum(i*i for i in range(100000))
end = time.time()
print(f'CPU测试完成，耗时: {(end-start)*1000:.2f}ms')
"
    
    if python3 -c "import torch; print(torch.cuda.is_available())" 2>/dev/null | grep -q "True"; then
        print_info "运行GPU测试..."
        python3 -c "
import torch
import time
if torch.cuda.is_available():
    start = time.time()
    x = torch.randn(1000, 1000).cuda()
    y = torch.mm(x, x)
    torch.cuda.synchronize()
    end = time.time()
    print(f'GPU测试完成，耗时: {(end-start)*1000:.2f}ms')
else:
    print('GPU不可用，跳过GPU测试')
"
    fi
    echo
    
    # 总结
    print_header "验证完成"
    print_success "快速验证已完成！"
    print_info "如需详细验证，请运行: python3 verify-environment.py"
    print_info "或运行: make test"
    echo
}

# 运行主函数
main "$@"
