/**
 * CUDA环境验证程序
 * 验证CUDA编译器、运行时和GPU功能
 */

#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <iostream>
#include <vector>
#include <chrono>
#include <iomanip>

// 错误检查宏
#define CUDA_CHECK(call) \
    do { \
        cudaError_t error = call; \
        if (error != cudaSuccess) { \
            std::cerr << "CUDA错误 " << __FILE__ << ":" << __LINE__ \
                      << " - " << cudaGetErrorString(error) << std::endl; \
            exit(1); \
        } \
    } while(0)

// 颜色输出类
class ColorOutput {
public:
    static const std::string RESET;
    static const std::string RED;
    static const std::string GREEN;
    static const std::string YELLOW;
    static const std::string BLUE;
    static const std::string MAGENTA;
    static const std::string CYAN;
    static const std::string BOLD;

    static void printColored(const std::string& text, const std::string& color) {
        std::cout << color << text << RESET << std::endl;
    }

    static void printSuccess(const std::string& text) {
        printColored("✓ " + text, GREEN);
    }

    static void printError(const std::string& text) {
        printColored("✗ " + text, RED);
    }

    static void printWarning(const std::string& text) {
        printColored("⚠ " + text, YELLOW);
    }

    static void printInfo(const std::string& text) {
        printColored("ℹ " + text, CYAN);
    }

    static void printHeader(const std::string& title) {
        std::cout << std::endl;
        printColored(std::string(60, '='), MAGENTA);
        printColored("  " + title, MAGENTA + BOLD);
        printColored(std::string(60, '='), MAGENTA);
    }
};

// 静态成员定义
const std::string ColorOutput::RESET = "\033[0m";
const std::string ColorOutput::RED = "\033[31m";
const std::string ColorOutput::GREEN = "\033[32m";
const std::string ColorOutput::YELLOW = "\033[33m";
const std::string ColorOutput::BLUE = "\033[34m";
const std::string ColorOutput::MAGENTA = "\033[35m";
const std::string ColorOutput::CYAN = "\033[36m";
const std::string ColorOutput::BOLD = "\033[1m";

// CUDA核函数：向量加法
__global__ void vectorAdd(const float* a, const float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] + b[idx];
    }
}

// CUDA核函数：矩阵乘法
__global__ void matrixMul(const float* a, const float* b, float* c, int n) {
    int row = blockIdx.y * blockDim.y + threadIdx.y;
    int col = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (row < n && col < n) {
        float sum = 0.0f;
        for (int k = 0; k < n; k++) {
            sum += a[row * n + k] * b[k * n + col];
        }
        c[row * n + col] = sum;
    }
}

// 检查CUDA设备信息
void checkCudaDevices() {
    ColorOutput::printHeader("CUDA设备信息");
    
    int deviceCount;
    CUDA_CHECK(cudaGetDeviceCount(&deviceCount));
    
    if (deviceCount == 0) {
        ColorOutput::printError("未找到CUDA设备");
        return;
    }
    
    ColorOutput::printSuccess("找到 " + std::to_string(deviceCount) + " 个CUDA设备");
    
    for (int i = 0; i < deviceCount; i++) {
        cudaDeviceProp prop;
        CUDA_CHECK(cudaGetDeviceProperties(&prop, i));
        
        std::cout << std::endl;
        ColorOutput::printInfo("设备 " + std::to_string(i) + ": " + prop.name);
        ColorOutput::printInfo("  计算能力: " + std::to_string(prop.major) + "." + std::to_string(prop.minor));
        ColorOutput::printInfo("  全局内存: " + std::to_string(prop.totalGlobalMem / (1024*1024)) + " MB");
        ColorOutput::printInfo("  共享内存/块: " + std::to_string(prop.sharedMemPerBlock / 1024) + " KB");
        ColorOutput::printInfo("  寄存器/块: " + std::to_string(prop.regsPerBlock));
        ColorOutput::printInfo("  最大线程/块: " + std::to_string(prop.maxThreadsPerBlock));
        ColorOutput::printInfo("  最大块维度: (" + 
                              std::to_string(prop.maxThreadsDim[0]) + ", " +
                              std::to_string(prop.maxThreadsDim[1]) + ", " +
                              std::to_string(prop.maxThreadsDim[2]) + ")");
        ColorOutput::printInfo("  最大网格维度: (" + 
                              std::to_string(prop.maxGridSize[0]) + ", " +
                              std::to_string(prop.maxGridSize[1]) + ", " +
                              std::to_string(prop.maxGridSize[2]) + ")");
        ColorOutput::printInfo("  多处理器数量: " + std::to_string(prop.multiProcessorCount));
        ColorOutput::printInfo("  内存时钟频率: " + std::to_string(prop.memoryClockRate / 1000) + " MHz");
        ColorOutput::printInfo("  内存总线宽度: " + std::to_string(prop.memoryBusWidth) + " bits");
        
        if (prop.canMapHostMemory) {
            ColorOutput::printSuccess("  支持主机内存映射");
        }
        
        if (prop.deviceOverlap) {
            ColorOutput::printSuccess("  支持设备重叠");
        }
    }
}

// 向量加法测试
bool testVectorAddition() {
    ColorOutput::printHeader("向量加法测试");
    
    const int n = 1000000;
    const size_t size = n * sizeof(float);
    
    // 分配主机内存
    std::vector<float> h_a(n), h_b(n), h_c(n);
    
    // 初始化数据
    for (int i = 0; i < n; i++) {
        h_a[i] = static_cast<float>(i);
        h_b[i] = static_cast<float>(i * 2);
    }
    
    // 分配设备内存
    float *d_a, *d_b, *d_c;
    CUDA_CHECK(cudaMalloc(&d_a, size));
    CUDA_CHECK(cudaMalloc(&d_b, size));
    CUDA_CHECK(cudaMalloc(&d_c, size));
    
    // 复制数据到设备
    CUDA_CHECK(cudaMemcpy(d_a, h_a.data(), size, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_b, h_b.data(), size, cudaMemcpyHostToDevice));
    
    // 配置执行参数
    int threadsPerBlock = 256;
    int blocksPerGrid = (n + threadsPerBlock - 1) / threadsPerBlock;
    
    // 执行核函数
    auto start = std::chrono::high_resolution_clock::now();
    vectorAdd<<<blocksPerGrid, threadsPerBlock>>>(d_a, d_b, d_c, n);
    CUDA_CHECK(cudaDeviceSynchronize());
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    // 复制结果回主机
    CUDA_CHECK(cudaMemcpy(h_c.data(), d_c, size, cudaMemcpyDeviceToHost));
    
    // 验证结果
    bool success = true;
    for (int i = 0; i < std::min(100, n); i++) {
        if (std::abs(h_c[i] - (h_a[i] + h_b[i])) > 1e-5) {
            success = false;
            break;
        }
    }
    
    // 清理内存
    cudaFree(d_a);
    cudaFree(d_b);
    cudaFree(d_c);
    
    if (success) {
        ColorOutput::printSuccess("向量加法测试通过");
        ColorOutput::printInfo("执行时间: " + std::to_string(duration.count()) + " 微秒");
        ColorOutput::printInfo("处理元素: " + std::to_string(n));
        ColorOutput::printInfo("吞吐量: " + std::to_string(n / (duration.count() / 1000000.0) / 1e6) + " M元素/秒");
    } else {
        ColorOutput::printError("向量加法测试失败");
    }
    
    return success;
}

// 矩阵乘法测试
bool testMatrixMultiplication() {
    ColorOutput::printHeader("矩阵乘法测试");
    
    const int n = 512;  // 512x512矩阵
    const size_t size = n * n * sizeof(float);
    
    // 分配主机内存
    std::vector<float> h_a(n * n), h_b(n * n), h_c(n * n);
    
    // 初始化数据
    for (int i = 0; i < n * n; i++) {
        h_a[i] = static_cast<float>(rand()) / RAND_MAX;
        h_b[i] = static_cast<float>(rand()) / RAND_MAX;
    }
    
    // 分配设备内存
    float *d_a, *d_b, *d_c;
    CUDA_CHECK(cudaMalloc(&d_a, size));
    CUDA_CHECK(cudaMalloc(&d_b, size));
    CUDA_CHECK(cudaMalloc(&d_c, size));
    
    // 复制数据到设备
    CUDA_CHECK(cudaMemcpy(d_a, h_a.data(), size, cudaMemcpyHostToDevice));
    CUDA_CHECK(cudaMemcpy(d_b, h_b.data(), size, cudaMemcpyHostToDevice));
    
    // 配置执行参数
    dim3 threadsPerBlock(16, 16);
    dim3 blocksPerGrid((n + threadsPerBlock.x - 1) / threadsPerBlock.x,
                       (n + threadsPerBlock.y - 1) / threadsPerBlock.y);
    
    // 执行核函数
    auto start = std::chrono::high_resolution_clock::now();
    matrixMul<<<blocksPerGrid, threadsPerBlock>>>(d_a, d_b, d_c, n);
    CUDA_CHECK(cudaDeviceSynchronize());
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // 复制结果回主机
    CUDA_CHECK(cudaMemcpy(h_c.data(), d_c, size, cudaMemcpyDeviceToHost));
    
    // 清理内存
    cudaFree(d_a);
    cudaFree(d_b);
    cudaFree(d_c);
    
    ColorOutput::printSuccess("矩阵乘法测试完成");
    ColorOutput::printInfo("矩阵大小: " + std::to_string(n) + "x" + std::to_string(n));
    ColorOutput::printInfo("执行时间: " + std::to_string(duration.count()) + " 毫秒");
    
    // 计算GFLOPS
    double gflops = (2.0 * n * n * n) / (duration.count() / 1000.0) / 1e9;
    ColorOutput::printInfo("性能: " + std::to_string(gflops) + " GFLOPS");
    
    return true;
}

// 内存带宽测试
void testMemoryBandwidth() {
    ColorOutput::printHeader("内存带宽测试");
    
    const size_t size = 256 * 1024 * 1024;  // 256MB
    const int iterations = 10;
    
    // 分配内存
    float *h_data = new float[size / sizeof(float)];
    float *d_data;
    CUDA_CHECK(cudaMalloc(&d_data, size));
    
    // 初始化数据
    for (size_t i = 0; i < size / sizeof(float); i++) {
        h_data[i] = static_cast<float>(i);
    }
    
    // 测试主机到设备传输
    auto start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < iterations; i++) {
        CUDA_CHECK(cudaMemcpy(d_data, h_data, size, cudaMemcpyHostToDevice));
    }
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    double h2d_bandwidth = (size * iterations / (1024.0 * 1024.0)) / (duration.count() / 1000.0);
    
    // 测试设备到主机传输
    start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < iterations; i++) {
        CUDA_CHECK(cudaMemcpy(h_data, d_data, size, cudaMemcpyDeviceToHost));
    }
    end = std::chrono::high_resolution_clock::now();
    
    duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    double d2h_bandwidth = (size * iterations / (1024.0 * 1024.0)) / (duration.count() / 1000.0);
    
    ColorOutput::printSuccess("内存带宽测试完成");
    ColorOutput::printInfo("主机到设备: " + std::to_string(h2d_bandwidth) + " MB/s");
    ColorOutput::printInfo("设备到主机: " + std::to_string(d2h_bandwidth) + " MB/s");
    
    // 清理内存
    delete[] h_data;
    cudaFree(d_data);
}

int main() {
    ColorOutput::printColored("CUDA环境验证工具", ColorOutput::MAGENTA + ColorOutput::BOLD);
    ColorOutput::printColored(std::string(60, '='), ColorOutput::MAGENTA);
    
    try {
        // 检查CUDA设备
        checkCudaDevices();
        
        // 运行测试
        testVectorAddition();
        testMatrixMultiplication();
        testMemoryBandwidth();
        
        ColorOutput::printHeader("验证完成");
        ColorOutput::printSuccess("所有CUDA测试通过！");
        
    } catch (const std::exception& e) {
        ColorOutput::printError("测试过程中发生错误: " + std::string(e.what()));
        return 1;
    }
    
    return 0;
}
