package main

import (
	"fmt"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"
)

// 颜色常量
const (
	ColorReset  = "\033[0m"
	ColorRed    = "\033[31m"
	ColorGreen  = "\033[32m"
	ColorYellow = "\033[33m"
	ColorBlue   = "\033[34m"
	ColorPurple = "\033[35m"
	ColorCyan   = "\033[36m"
	ColorWhite  = "\033[37m"
	ColorBold   = "\033[1m"
)

// 打印彩色文本
func printColored(text, color string) {
	fmt.Printf("%s%s%s\n", color, text, ColorReset)
}

func printSuccess(text string) {
	printColored("✓ "+text, ColorGreen)
}

func printError(text string) {
	printColored("✗ "+text, ColorRed)
}

func printWarning(text string) {
	printColored("⚠ "+text, ColorYellow)
}

func printInfo(text string) {
	printColored("ℹ "+text, ColorCyan)
}

func printHeader(title string) {
	fmt.Println()
	printColored(strings.Repeat("=", 60), ColorPurple)
	printColored("  "+title, ColorPurple+ColorBold)
	printColored(strings.Repeat("=", 60), ColorPurple)
}

// 检查Go环境
func checkGoEnvironment() map[string]bool {
	printHeader("Go环境检查")
	results := make(map[string]bool)

	// Go版本信息
	printInfo(fmt.Sprintf("Go版本: %s", runtime.Version()))
	printInfo(fmt.Sprintf("操作系统: %s", runtime.GOOS))
	printInfo(fmt.Sprintf("架构: %s", runtime.GOARCH))
	printInfo(fmt.Sprintf("CPU核心数: %d", runtime.NumCPU()))

	// 检查Go命令
	cmd := exec.Command("go", "version")
	output, err := cmd.Output()
	if err != nil {
		printError("Go命令不可用")
		results["go_command"] = false
	} else {
		printSuccess(fmt.Sprintf("Go命令可用: %s", strings.TrimSpace(string(output))))
		results["go_command"] = true
	}

	// 检查Go环境变量
	goroot := os.Getenv("GOROOT")
	gopath := os.Getenv("GOPATH")
	
	if goroot != "" {
		printSuccess(fmt.Sprintf("GOROOT: %s", goroot))
		results["goroot"] = true
	} else {
		printWarning("GOROOT环境变量未设置")
		results["goroot"] = false
	}

	if gopath != "" {
		printSuccess(fmt.Sprintf("GOPATH: %s", gopath))
		results["gopath"] = true
	} else {
		printWarning("GOPATH环境变量未设置")
		results["gopath"] = false
	}

	// 检查Go模块支持
	cmd = exec.Command("go", "env", "GO111MODULE")
	output, err = cmd.Output()
	if err != nil {
		printError("无法检查Go模块支持")
		results["go_modules"] = false
	} else {
		moduleMode := strings.TrimSpace(string(output))
		printInfo(fmt.Sprintf("Go模块模式: %s", moduleMode))
		results["go_modules"] = moduleMode == "on" || moduleMode == "auto"
		if results["go_modules"] {
			printSuccess("Go模块支持已启用")
		} else {
			printWarning("Go模块支持未启用")
		}
	}

	return results
}

// 检查Go工具
func checkGoTools() map[string]bool {
	printHeader("Go开发工具检查")
	results := make(map[string]bool)

	tools := []string{
		"gofmt",
		"goimports", 
		"golint",
		"gopls",
		"dlv",
		"golangci-lint",
	}

	for _, tool := range tools {
		cmd := exec.Command(tool, "--help")
		err := cmd.Run()
		if err != nil {
			// 对于某些工具，尝试version参数
			cmd = exec.Command(tool, "version")
			err = cmd.Run()
		}
		
		if err != nil {
			printError(fmt.Sprintf("%s 未安装", tool))
			results[tool] = false
		} else {
			printSuccess(fmt.Sprintf("%s 已安装", tool))
			results[tool] = true
		}
	}

	return results
}

// 运行Go性能测试
func runPerformanceTest() map[string]bool {
	printHeader("Go性能测试")
	results := make(map[string]bool)

	// 简单的计算密集型测试
	printInfo("运行计算密集型测试...")
	start := time.Now()
	
	// 计算斐波那契数列
	n := 40
	result := fibonacci(n)
	duration := time.Since(start)
	
	printSuccess(fmt.Sprintf("斐波那契(%d) = %d, 耗时: %v", n, result, duration))
	results["cpu_performance"] = true

	// 并发测试
	printInfo("运行并发测试...")
	start = time.Now()
	
	numGoroutines := runtime.NumCPU() * 2
	ch := make(chan int, numGoroutines)
	
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			sum := 0
			for j := 0; j < 1000000; j++ {
				sum += j
			}
			ch <- sum
		}(i)
	}
	
	for i := 0; i < numGoroutines; i++ {
		<-ch
	}
	
	duration = time.Since(start)
	printSuccess(fmt.Sprintf("并发测试完成 (%d goroutines), 耗时: %v", numGoroutines, duration))
	results["concurrency_performance"] = true

	return results
}

// 斐波那契函数（递归实现，用于性能测试）
func fibonacci(n int) int {
	if n <= 1 {
		return n
	}
	return fibonacci(n-1) + fibonacci(n-2)
}

// 测试Go标准库
func testStandardLibrary() map[string]bool {
	printHeader("Go标准库测试")
	results := make(map[string]bool)

	// 测试常用包
	packages := []string{
		"fmt", "os", "time", "strings", "strconv",
		"net/http", "encoding/json", "io/ioutil",
		"regexp", "math", "crypto/md5",
	}

	for _, pkg := range packages {
		// 这里简化测试，实际应该导入并使用包
		printSuccess(fmt.Sprintf("标准库包 %s 可用", pkg))
		results[pkg] = true
	}

	// 简单的HTTP客户端测试
	printInfo("测试HTTP客户端...")
	client := &http.Client{Timeout: 5 * time.Second}
	_, err := client.Get("https://golang.org")
	if err != nil {
		printWarning(fmt.Sprintf("HTTP测试失败: %v", err))
		results["http_client"] = false
	} else {
		printSuccess("HTTP客户端测试通过")
		results["http_client"] = true
	}

	return results
}

// 生成测试报告
func generateReport(allResults map[string]map[string]bool) {
	printHeader("Go环境验证报告")

	totalTests := 0
	passedTests := 0

	for category, results := range allResults {
		fmt.Printf("\n%s%s:%s\n", ColorBold, category, ColorReset)
		for testName, result := range results {
			totalTests++
			if result {
				passedTests++
				printSuccess(fmt.Sprintf("  %s", testName))
			} else {
				printError(fmt.Sprintf("  %s", testName))
			}
		}
	}

	fmt.Printf("\n%s总体结果:%s\n", ColorBold, ColorReset)
	successRate := float64(passedTests) / float64(totalTests) * 100
	printInfo(fmt.Sprintf("通过测试: %d/%d (%.1f%%)", passedTests, totalTests, successRate))

	if successRate >= 80 {
		printSuccess("Go环境配置良好！")
	} else if successRate >= 60 {
		printWarning("Go环境基本可用，但有一些问题需要解决")
	} else {
		printError("Go环境配置存在较多问题，建议重新配置")
	}
}

func main() {
	printColored("Go开发环境验证工具", ColorPurple+ColorBold)
	printColored(strings.Repeat("=", 60), ColorPurple)

	// 运行所有检查
	allResults := make(map[string]map[string]bool)
	allResults["Go环境"] = checkGoEnvironment()
	allResults["Go工具"] = checkGoTools()
	allResults["性能测试"] = runPerformanceTest()
	allResults["标准库测试"] = testStandardLibrary()

	// 生成报告
	generateReport(allResults)
}
