# Docker Compose配置文件
# 多语言机器学习开发环境
# 支持GPU加速和完整的开发工具链

version: '3.8'

services:
  mldev:
    build:
      context: .
      dockerfile: Dockerfile
    image: mldev:latest
    container_name: mldev-container
    
    # GPU支持配置
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    # 环境变量
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - DISPLAY=${DISPLAY:-:0}
      - CUDA_VISIBLE_DEVICES=0
      - PYTHONPATH=/workspace
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=mldev2024
    
    # 端口映射
    ports:
      - "8888:8888"    # Jupyter Lab
      - "8000:8000"    # FastAPI/Django
      - "3000:3000"    # React/Node.js
      - "8080:8080"    # 通用Web服务
      - "6006:6006"    # TensorBoard
      - "5000:5000"    # Flask
      - "9000:9000"    # 其他服务
    
    # 卷挂载
    volumes:
      # 工作目录挂载（Windows路径格式）
      - type: bind
        source: .
        target: /workspace
        bind:
          propagation: cached
      
      # Docker socket挂载（用于在容器内使用Docker）
      - /var/run/docker.sock:/var/run/docker.sock
      
      # 缓存目录（提升性能）
      - conda-cache:/opt/conda/pkgs
      - pip-cache:/root/.cache/pip
      - go-cache:/workspace/go/pkg
      
      # 模型和数据缓存
      - huggingface-cache:/root/.cache/huggingface
      - torch-cache:/root/.cache/torch
    
    # 网络配置
    networks:
      - mldev-network
    
    # 共享内存大小（重要：用于PyTorch DataLoader）
    shm_size: '8gb'
    
    # 内存限制（根据64GB RAM调整）
    mem_limit: 32g
    mem_reservation: 16g
    
    # CPU限制
    cpus: '12'  # 为13600KF预留部分核心
    
    # 重启策略
    restart: unless-stopped
    
    # 工作目录
    working_dir: /workspace
    
    # 保持容器运行
    tty: true
    stdin_open: true
    
    # 特权模式（如果需要访问硬件）
    privileged: false
    
    # 安全选项
    security_opt:
      - seccomp:unconfined
    
    # 设备挂载（如果需要访问特定设备）
    devices:
      - /dev/dri:/dev/dri  # GPU设备
    
    # 健康检查
    healthcheck:
      test: ["CMD", "nvidia-smi"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# 网络配置
networks:
  mldev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 卷配置
volumes:
  conda-cache:
    driver: local
  pip-cache:
    driver: local
  go-cache:
    driver: local
  huggingface-cache:
    driver: local
  torch-cache:
    driver: local
