# ================================================================
# 多语言机器学习开发环境启动脚本 (PowerShell版本)
# 支持CUDA GPU加速的Python、C++、Go开发环境
# ================================================================

param(
    [string]$Action = "menu",
    [switch]$NoGPU = $false,
    [switch]$Rebuild = $false,
    [switch]$Verbose = $false
)

# 设置错误处理
$ErrorActionPreference = "Continue"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✓ $Message" "Green"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "✗ $Message" "Red"
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠ $Message" "Yellow"
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ $Message" "Cyan"
}

# 显示标题
function Show-Header {
    Clear-Host
    Write-ColorOutput @"
========================================
   多语言机器学习开发环境启动器
========================================
"@ "Magenta"
    Write-Host ""
}

# 检查Docker环境
function Test-DockerEnvironment {
    Write-Info "检查Docker环境..."

    try {
        $dockerVersion = docker --version
        Write-Success "Docker已安装: $dockerVersion"
    }
    catch {
        Write-Error "Docker未安装或未启动"
        Write-Host "请先安装Docker Desktop: https://www.docker.com/products/docker-desktop"
        exit 1
    }

    try {
        $composeVersion = docker compose version
        Write-Success "Docker Compose可用: $composeVersion"
    }
    catch {
        Write-Error "Docker Compose不可用，请更新Docker Desktop"
        exit 1
    }

    # 检查NVIDIA Docker支持
    if (-not $NoGPU) {
        Write-Info "检查NVIDIA Docker支持..."
        try {
            $null = docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu22.04 nvidia-smi 2>$null
            Write-Success "NVIDIA Docker支持已启用"
            return $true
        }
        catch {
            Write-Warning "NVIDIA Docker支持不可用，将以CPU模式运行"
            Write-Host "请安装NVIDIA Container Toolkit"
            return $false
        }
    }
    return $false
}
}

# 设置环境变量
function Set-Environment {
    $env:COMPOSE_PROJECT_NAME = "mldev"
    $env:DOCKER_BUILDKIT = "1"
    $env:COMPOSE_DOCKER_CLI_BUILD = "1"
    
    Write-Info "当前工作目录: $(Get-Location)"
    Write-Info "容器工作目录: /workspace"
}

# 显示环境信息
function Show-EnvironmentInfo {
    Write-ColorOutput @"

========================================
   开发环境信息
========================================
容器名称: mldev-container
工作目录: /workspace

可用端口:
  - Jupyter Lab:  http://localhost:8888 (token: mldev2024)
  - Web服务:      http://localhost:8000
  - React/Node:   http://localhost:3000
  - 通用服务:     http://localhost:8080
  - TensorBoard:  http://localhost:6006

进入容器: docker compose exec mldev /bin/zsh
========================================

"@ "Green"
}

# 构建并启动环境
function Start-Environment {
    param([switch]$Build = $false)
    
    Write-Info "$(if($Build){'构建并'}else{''})启动开发环境..."
    
    try {
        if ($Build -or $Rebuild) {
            docker compose up -d --build
        } else {
            docker compose up -d
        }
        Write-Success "开发环境已启动"
        Show-EnvironmentInfo
    }
    catch {
        Write-Error "启动失败: $($_.Exception.Message)"
    }
}

# 停止环境
function Stop-Environment {
    Write-Info "停止开发环境..."
    try {
        docker compose down
        Write-Success "开发环境已停止"
    }
    catch {
        Write-Error "停止失败: $($_.Exception.Message)"
    }
}

# 重建环境
function Rebuild-Environment {
    Write-Info "重建开发环境（这可能需要较长时间）..."
    try {
        docker compose down -v
        docker compose build --no-cache
        docker compose up -d
        Write-Success "开发环境重建完成"
        Show-EnvironmentInfo
    }
    catch {
        Write-Error "重建失败: $($_.Exception.Message)"
    }
}

# 查看状态
function Show-Status {
    Write-Info "环境状态:"
    docker compose ps
    Write-Host ""
    docker compose top
}

# 进入Shell
function Enter-Shell {
    Write-Info "进入容器Shell (zsh)..."
    Write-Host "提示: 使用 'exit' 命令退出容器" -ForegroundColor Yellow
    docker compose exec mldev /bin/zsh
}
}

# 启动Jupyter Lab
function Start-JupyterLab {
    Write-Info "启动Jupyter Lab..."
    try {
        docker compose exec -d mldev jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --token=mldev2024
        Write-Success "Jupyter Lab已启动"
        Write-Host "访问地址: http://localhost:8888" -ForegroundColor Green
        Write-Host "访问令牌: mldev2024" -ForegroundColor Green
        
        # 尝试打开浏览器
        try {
            Start-Process "http://localhost:8888"
        }
        catch {
            Write-Info "请手动打开浏览器访问 http://localhost:8888"
        }
    }
    catch {
        Write-Error "启动Jupyter Lab失败: $($_.Exception.Message)"
    }
}

# 查看日志
function Show-Logs {
    Write-Info "查看容器日志 (按Ctrl+C退出)..."
    docker compose logs -f mldev
}

# 清理环境
function Clear-Environment {
    $confirm = Read-Host "这将删除所有容器、镜像和卷，确定要继续吗？(输入 'yes' 确认)"
    if ($confirm -eq "yes") {
        Write-Info "清理环境..."
        try {
            docker compose down -v --rmi all
            docker system prune -f
            Write-Success "环境清理完成"
        }
        catch {
            Write-Error "清理失败: $($_.Exception.Message)"
        }
    } else {
        Write-Info "取消清理操作"
    }
}

# 显示菜单
function Show-Menu {
    while ($true) {
        Write-Host ""
        Write-ColorOutput "请选择操作:" "Yellow"
        Write-Host "1. 构建并启动开发环境"
        Write-Host "2. 启动已有环境"
        Write-Host "3. 停止环境"
        Write-Host "4. 重建环境（清除缓存）"
        Write-Host "5. 查看环境状态"
        Write-Host "6. 进入容器Shell"
        Write-Host "7. 启动Jupyter Lab"
        Write-Host "8. 查看日志"
        Write-Host "9. 清理环境"
        Write-Host "0. 退出"
        Write-Host ""
        
        $choice = Read-Host "请输入选项 (0-9)"
        
        switch ($choice) {
            "1" { Start-Environment -Build }
            "2" { Start-Environment }
            "3" { Stop-Environment }
            "4" { Rebuild-Environment }
            "5" { Show-Status }
            "6" { Enter-Shell }
            "7" { Start-JupyterLab }
            "8" { Show-Logs }
            "9" { Clear-Environment }
            "0" { 
                Write-ColorOutput "`n感谢使用多语言机器学习开发环境！`n" "Magenta"
                exit 0 
            }
            default { Write-Warning "无效选项，请重新选择" }
        }
    }
}

# 主函数
function Main {
    Show-Header
    $gpuSupport = Test-DockerEnvironment
    Set-Environment
    
    # 根据参数执行相应操作
    switch ($Action.ToLower()) {
        "build" { Start-Environment -Build }
        "start" { Start-Environment }
        "stop" { Stop-Environment }
        "rebuild" { Rebuild-Environment }
        "status" { Show-Status }
        "shell" { Enter-Shell }
        "jupyter" { Start-JupyterLab }
        "logs" { Show-Logs }
        "clean" { Clear-Environment }
        default { Show-Menu }
    }
}

# 执行主函数
Main
