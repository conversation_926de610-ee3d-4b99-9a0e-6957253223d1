# 多语言机器学习开发环境

基于Docker的完整多语言开发环境，专为机器学习和系统开发设计，支持Python、C++、Go和CUDA开发。

## 🚀 特性

- **多语言支持**: Python 3.10+、C++、Go 1.21+、CUDA 11.8
- **GPU加速**: 完整的NVIDIA CUDA支持，适配RTX 4070 Super
- **机器学习生态**: PyTorch、scikit-learn、Jupyter Lab等
- **开发工具**: 完整的编译器、调试器和代码分析工具
- **容器化**: 基于Docker的隔离环境，易于部署和管理
- **性能优化**: 针对Intel 13600KF + RTX 4070 Super + 64GB RAM优化

## 📋 系统要求

### 硬件要求
- **CPU**: Intel 13600KF 或同等性能处理器
- **GPU**: NVIDIA RTX 4070 Super 或支持CUDA 11.8+的显卡
- **内存**: 64GB RAM（推荐）
- **存储**: 至少50GB可用空间

### 软件要求
- **操作系统**: Windows 10/11 (64位)
- **Docker Desktop**: 最新版本，启用WSL2后端
- **NVIDIA Container Toolkit**: 用于GPU支持
- **PowerShell**: 5.1+ 或 PowerShell Core 7+

## 🛠️ 安装指南

### 1. 安装Docker Desktop

1. 下载并安装 [Docker Desktop](https://www.docker.com/products/docker-desktop)
2. 启用WSL2后端
3. 确保Docker服务正在运行

### 2. 安装NVIDIA Container Toolkit

```powershell
# 在PowerShell中运行（管理员权限）
# 下载并安装NVIDIA Container Toolkit
# 详细步骤请参考: https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html
```

### 3. 克隆或下载项目

```bash
git clone <repository-url>
cd llm-dev
```

## 🚀 快速开始

### 方法一：使用批处理脚本（推荐新手）

1. 双击运行 `start-mldev.bat`
2. 选择 "1. 构建并启动开发环境"
3. 等待构建完成（首次构建需要20-30分钟）

### 方法二：使用PowerShell脚本（推荐）

```powershell
# 启动环境
.\start-mldev.ps1

# 或直接构建并启动
.\start-mldev.ps1 -Action build
```

### 方法三：使用Docker Compose

```bash
# 构建并启动
docker compose up -d --build

# 进入容器
docker compose exec mldev /bin/zsh
```

## 📚 使用指南

### 进入开发环境

```bash
# 方法1: 使用启动脚本菜单选项6
# 方法2: 直接使用Docker命令
docker compose exec mldev /bin/zsh
```

### 启动Jupyter Lab

```bash
# 在容器内运行
jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --token=mldev2024

# 或使用启动脚本菜单选项7
```

访问地址: http://localhost:8888  
访问令牌: `mldev2024`

### 验证环境

```bash
# 在容器内运行完整验证
make test

# 快速验证
make quick-test

# 单独验证各组件
python3 verify-environment.py
./verify-go
./verify-cuda
```

## 🔧 开发环境详情

### Python环境
- **版本**: Python 3.10+
- **包管理**: miniConda
- **主要库**: PyTorch, NumPy, Pandas, Matplotlib, Jupyter
- **开发工具**: Black, Flake8, Pytest, MyPy

### C++环境
- **编译器**: GCC/G++ 9+
- **构建工具**: CMake
- **库**: Boost, Eigen, OpenCV

### Go环境
- **版本**: Go 1.21+
- **工具**: gopls, dlv, goimports, golangci-lint
- **模块**: 支持Go Modules

### CUDA环境
- **版本**: CUDA 11.8
- **库**: cuDNN
- **工具**: NVCC编译器
- **支持**: CUDA C/C++开发

### 目录结构

```
/workspace/
├── python/     # Python项目
├── cpp/        # C++项目
├── go/         # Go项目
├── cuda/       # CUDA项目
├── notebooks/  # Jupyter笔记本
├── data/       # 数据文件
└── models/     # 模型文件
```

## 🌐 端口映射

| 服务 | 容器端口 | 主机端口 | 说明 |
|------|----------|----------|------|
| Jupyter Lab | 8888 | 8888 | 开发环境 |
| Web服务 | 8000 | 8000 | FastAPI/Django |
| React/Node | 3000 | 3000 | 前端开发 |
| 通用服务 | 8080 | 8080 | 其他Web服务 |
| TensorBoard | 6006 | 6006 | 模型可视化 |
| Flask | 5000 | 5000 | Python Web |

## 🔍 环境验证

### 自动验证脚本

```bash
# Python环境验证
python3 verify-environment.py

# Go环境验证
go run verify-go.go
# 或编译后运行
make verify-go && ./verify-go

# CUDA环境验证
make verify-cuda && ./verify-cuda
```

### 手动验证

```python
# Python + PyTorch + CUDA验证
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"GPU数量: {torch.cuda.device_count()}")
if torch.cuda.is_available():
    print(f"GPU名称: {torch.cuda.get_device_name(0)}")
```

```go
// Go验证
package main
import "fmt"
func main() {
    fmt.Println("Go环境正常")
}
```

```bash
# CUDA验证
nvidia-smi
nvcc --version
```

## 🛠️ 常用命令

### 容器管理

```bash
# 启动环境
docker compose up -d

# 停止环境
docker compose down

# 重建环境
docker compose up -d --build --force-recreate

# 查看状态
docker compose ps

# 查看日志
docker compose logs -f mldev
```

### 开发工作流

```bash
# 进入容器
docker compose exec mldev /bin/zsh

# 激活Python环境（已自动激活）
conda activate mldev

# 安装新的Python包
pip install package-name

# 安装Go包
go get package-name

# 编译CUDA程序
nvcc -o program program.cu
```

## 🔧 自定义配置

### 修改Python环境

```bash
# 在容器内安装新包
pip install new-package

# 或修改Dockerfile中的pip install部分
```

### 添加新的开发工具

编辑 `Dockerfile`，在相应部分添加：

```dockerfile
RUN apt-get update && apt-get install -y \
    your-new-tool \
    && rm -rf /var/lib/apt/lists/*
```

### 修改端口映射

编辑 `docker-compose.yml` 的 ports 部分：

```yaml
ports:
  - "新端口:容器端口"
```

## 📊 性能优化

### 内存优化
- 容器默认分配32GB内存
- 共享内存设置为8GB（用于PyTorch DataLoader）
- 可在docker-compose.yml中调整

### GPU优化
- 启用所有GPU设备
- 配置CUDA环境变量
- 优化CUDA库加载路径

### 存储优化
- 使用卷缓存提升性能
- 分离代码和数据存储
- 定期清理Docker缓存

## 🐛 故障排除

### 常见问题

#### 1. Docker启动失败
```bash
# 检查Docker服务状态
docker version
docker info

# 重启Docker Desktop
```

#### 2. GPU不可用
```bash
# 检查NVIDIA驱动
nvidia-smi

# 检查Docker GPU支持
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu22.04 nvidia-smi
```

#### 3. 端口冲突
```bash
# 检查端口占用
netstat -an | findstr :8888

# 修改docker-compose.yml中的端口映射
```

#### 4. 内存不足
```bash
# 检查系统内存
wmic computersystem get TotalPhysicalMemory

# 调整docker-compose.yml中的内存限制
```

### 日志查看

```bash
# 查看容器日志
docker compose logs mldev

# 实时查看日志
docker compose logs -f mldev

# 查看特定时间的日志
docker compose logs --since="2024-01-01T00:00:00" mldev
```

## 🔄 更新和维护

### 更新环境

```bash
# 拉取最新镜像
docker compose pull

# 重建容器
docker compose up -d --build

# 清理旧镜像
docker image prune -f
```

### 备份和恢复

```bash
# 备份工作目录（自动挂载，无需特殊操作）
# 备份容器配置
docker compose config > backup-compose.yml

# 导出镜像
docker save mldev:latest > mldev-backup.tar
```

## 📝 开发建议

### 项目组织
- 按语言分类组织代码
- 使用Git进行版本控制
- 定期备份重要数据

### 最佳实践
- 使用虚拟环境隔离Python项目
- 遵循各语言的编码规范
- 编写单元测试验证功能
- 使用Jupyter进行原型开发

### 性能监控
- 使用nvidia-smi监控GPU使用
- 使用htop监控CPU和内存
- 定期清理临时文件

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个开发环境！

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🆘 获取帮助

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 运行环境验证脚本诊断问题
3. 查看容器日志获取详细错误信息
4. 查看FAQ.md获取更多解决方案
5. 提交Issue描述问题和环境信息

## 📚 相关文档

- [FAQ.md](FAQ.md) - 常见问题解决方案
- [TROUBLESHOOTING.md](TROUBLESHOOTING.md) - 详细故障排除指南
- [DEVELOPMENT.md](DEVELOPMENT.md) - 开发指南和最佳实践

---

**祝您开发愉快！** 🎉
