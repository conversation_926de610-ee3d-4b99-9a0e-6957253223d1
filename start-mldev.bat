@echo off
REM ================================================================
REM 多语言机器学习开发环境启动脚本 (Windows批处理版本)
REM 支持CUDA GPU加速的Python、C++、Go开发环境
REM ================================================================

echo.
echo ========================================
echo   多语言机器学习开发环境启动器
echo ========================================
echo.

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker未安装或未启动，请先安装Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

REM 检查Docker Compose是否可用
docker compose version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Docker Compose不可用，请更新Docker Desktop到最新版本
    pause
    exit /b 1
)

REM 检查NVIDIA Docker支持
echo [信息] 检查NVIDIA Docker支持...
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu22.04 nvidia-smi >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] NVIDIA Docker支持不可用，将以CPU模式运行
    echo 请确保已安装NVIDIA Container Toolkit
    echo 安装指南: https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html
    echo.
    set GPU_SUPPORT=false
) else (
    echo [成功] NVIDIA Docker支持已启用
    set GPU_SUPPORT=true
)

REM 设置环境变量
set COMPOSE_PROJECT_NAME=mldev
set DOCKER_BUILDKIT=1
set COMPOSE_DOCKER_CLI_BUILD=1

echo.
echo [信息] 当前工作目录: %CD%
echo [信息] 容器工作目录: /workspace
echo.

REM 显示菜单
:menu
echo 请选择操作:
echo 1. 构建并启动开发环境
echo 2. 启动已有环境
echo 3. 停止环境
echo 4. 重建环境（清除缓存）
echo 5. 查看环境状态
echo 6. 进入容器Shell
echo 7. 启动Jupyter Lab
echo 8. 查看日志
echo 9. 清理环境
echo 0. 退出
echo.
set /p choice="请输入选项 (0-9): "

if "%choice%"=="1" goto build_start
if "%choice%"=="2" goto start
if "%choice%"=="3" goto stop
if "%choice%"=="4" goto rebuild
if "%choice%"=="5" goto status
if "%choice%"=="6" goto shell
if "%choice%"=="7" goto jupyter
if "%choice%"=="8" goto logs
if "%choice%"=="9" goto cleanup
if "%choice%"=="0" goto exit
echo [错误] 无效选项，请重新选择
goto menu

:build_start
echo.
echo [信息] 构建并启动开发环境...
docker compose up -d --build
if %errorlevel% equ 0 (
    echo [成功] 开发环境已启动
    call :show_info
) else (
    echo [错误] 启动失败，请检查错误信息
)
goto menu

:start
echo.
echo [信息] 启动开发环境...
docker compose up -d
if %errorlevel% equ 0 (
    echo [成功] 开发环境已启动
    call :show_info
) else (
    echo [错误] 启动失败，请检查错误信息
)
goto menu

:stop
echo.
echo [信息] 停止开发环境...
docker compose down
echo [成功] 开发环境已停止
goto menu

:rebuild
echo.
echo [信息] 重建开发环境（这可能需要较长时间）...
docker compose down -v
docker compose build --no-cache
docker compose up -d
if %errorlevel% equ 0 (
    echo [成功] 开发环境重建完成
    call :show_info
) else (
    echo [错误] 重建失败，请检查错误信息
)
goto menu

:status
echo.
echo [信息] 环境状态:
docker compose ps
echo.
docker compose top
goto menu

:shell
echo.
echo [信息] 进入容器Shell (zsh)...
echo 提示: 使用 'exit' 命令退出容器
docker compose exec mldev /bin/zsh
goto menu

:jupyter
echo.
echo [信息] 启动Jupyter Lab...
echo 正在启动Jupyter Lab服务器...
docker compose exec -d mldev jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --token=mldev2024
echo.
echo [成功] Jupyter Lab已启动
echo 访问地址: http://localhost:8888
echo 访问令牌: mldev2024
echo.
echo 按任意键返回菜单...
pause >nul
goto menu

:logs
echo.
echo [信息] 查看容器日志 (按Ctrl+C退出)...
docker compose logs -f mldev
goto menu

:cleanup
echo.
echo [警告] 这将删除所有容器、镜像和卷，确定要继续吗？
set /p confirm="输入 'yes' 确认清理: "
if /i "%confirm%"=="yes" (
    echo [信息] 清理环境...
    docker compose down -v --rmi all
    docker system prune -f
    echo [成功] 环境清理完成
) else (
    echo [信息] 取消清理操作
)
goto menu

:show_info
echo.
echo ========================================
echo   开发环境信息
echo ========================================
echo 容器名称: mldev-container
echo 工作目录: /workspace
echo.
echo 可用端口:
echo   - Jupyter Lab:  http://localhost:8888 (token: mldev2024)
echo   - Web服务:      http://localhost:8000
echo   - React/Node:   http://localhost:3000
echo   - 通用服务:     http://localhost:8080
echo   - TensorBoard:  http://localhost:6006
echo.
echo 进入容器: docker compose exec mldev /bin/zsh
echo 或使用菜单选项 6
echo ========================================
echo.
exit /b 0

:exit
echo.
echo 感谢使用多语言机器学习开发环境！
echo.
exit /b 0
