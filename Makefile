# Makefile for Multi-language Development Environment Verification
# 多语言开发环境验证工具编译脚本

# 编译器设置
NVCC = nvcc
GO = go
PYTHON = python3

# 编译标志
NVCC_FLAGS = -std=c++11 -O2
GO_FLAGS = -ldflags="-s -w"

# 目标文件
CUDA_TARGET = verify-cuda
GO_TARGET = verify-go
PYTHON_SCRIPT = verify-environment.py

# 默认目标
.PHONY: all clean test help

all: $(CUDA_TARGET) $(GO_TARGET)
	@echo "所有验证工具编译完成"

# 编译CUDA验证程序
$(CUDA_TARGET): verify-cuda.cu
	@echo "编译CUDA验证程序..."
	@if command -v $(NVCC) >/dev/null 2>&1; then \
		$(NVCC) $(NVCC_FLAGS) -o $(CUDA_TARGET) verify-cuda.cu; \
		echo "✓ CUDA验证程序编译成功"; \
	else \
		echo "⚠ NVCC编译器未找到，跳过CUDA程序编译"; \
	fi

# 编译Go验证程序
$(GO_TARGET): verify-go.go
	@echo "编译Go验证程序..."
	@if command -v $(GO) >/dev/null 2>&1; then \
		$(GO) build $(GO_FLAGS) -o $(GO_TARGET) verify-go.go; \
		echo "✓ Go验证程序编译成功"; \
	else \
		echo "⚠ Go编译器未找到，跳过Go程序编译"; \
	fi

# 运行所有验证测试
test: all
	@echo "=========================================="
	@echo "  运行多语言环境验证测试"
	@echo "=========================================="
	
	@echo "\n1. Python环境验证:"
	@if command -v $(PYTHON) >/dev/null 2>&1; then \
		$(PYTHON) $(PYTHON_SCRIPT); \
	else \
		echo "✗ Python解释器未找到"; \
	fi
	
	@echo "\n2. Go环境验证:"
	@if [ -f $(GO_TARGET) ]; then \
		./$(GO_TARGET); \
	else \
		echo "✗ Go验证程序未编译"; \
	fi
	
	@echo "\n3. CUDA环境验证:"
	@if [ -f $(CUDA_TARGET) ]; then \
		./$(CUDA_TARGET); \
	else \
		echo "✗ CUDA验证程序未编译"; \
	fi

# 单独运行Python验证
test-python:
	@echo "运行Python环境验证..."
	@$(PYTHON) $(PYTHON_SCRIPT)

# 单独运行Go验证
test-go: $(GO_TARGET)
	@echo "运行Go环境验证..."
	@./$(GO_TARGET)

# 单独运行CUDA验证
test-cuda: $(CUDA_TARGET)
	@echo "运行CUDA环境验证..."
	@./$(CUDA_TARGET)

# 检查编译器可用性
check-compilers:
	@echo "检查编译器可用性:"
	@echo -n "Python: "
	@if command -v $(PYTHON) >/dev/null 2>&1; then \
		echo "✓ 可用 ($$($(PYTHON) --version))"; \
	else \
		echo "✗ 不可用"; \
	fi
	
	@echo -n "Go: "
	@if command -v $(GO) >/dev/null 2>&1; then \
		echo "✓ 可用 ($$($(GO) version))"; \
	else \
		echo "✗ 不可用"; \
	fi
	
	@echo -n "NVCC: "
	@if command -v $(NVCC) >/dev/null 2>&1; then \
		echo "✓ 可用 ($$($(NVCC) --version | grep release))"; \
	else \
		echo "✗ 不可用"; \
	fi

# 清理编译产物
clean:
	@echo "清理编译产物..."
	@rm -f $(CUDA_TARGET) $(GO_TARGET)
	@rm -f *.o *.exe
	@echo "✓ 清理完成"

# 安装依赖（在容器内运行）
install-deps:
	@echo "安装验证工具依赖..."
	
	@echo "更新Go工具..."
	@if command -v $(GO) >/dev/null 2>&1; then \
		$(GO) install golang.org/x/tools/gopls@latest; \
		$(GO) install github.com/go-delve/delve/cmd/dlv@latest; \
		$(GO) install golang.org/x/tools/cmd/goimports@latest; \
		$(GO) install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
		echo "✓ Go工具安装完成"; \
	fi
	
	@echo "检查Python包..."
	@if command -v $(PYTHON) >/dev/null 2>&1; then \
		$(PYTHON) -c "import torch, numpy, pandas; print('✓ 主要Python包可用')"; \
	fi

# 快速验证（仅检查基本功能）
quick-test:
	@echo "快速环境验证:"
	
	@echo -n "Python: "
	@if $(PYTHON) -c "import sys; print(f'✓ {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}')" 2>/dev/null; then \
		true; \
	else \
		echo "✗ 不可用"; \
	fi
	
	@echo -n "PyTorch: "
	@if $(PYTHON) -c "import torch; print(f'✓ {torch.__version__}')" 2>/dev/null; then \
		true; \
	else \
		echo "✗ 不可用"; \
	fi
	
	@echo -n "CUDA: "
	@if $(PYTHON) -c "import torch; print('✓ 可用' if torch.cuda.is_available() else '✗ 不可用')" 2>/dev/null; then \
		true; \
	else \
		echo "✗ 检查失败"; \
	fi
	
	@echo -n "Go: "
	@if command -v $(GO) >/dev/null 2>&1; then \
		echo "✓ $$($(GO) version | cut -d' ' -f3)"; \
	else \
		echo "✗ 不可用"; \
	fi
	
	@echo -n "NVCC: "
	@if command -v $(NVCC) >/dev/null 2>&1; then \
		echo "✓ $$($(NVCC) --version | grep -o 'release [0-9.]*' | cut -d' ' -f2)"; \
	else \
		echo "✗ 不可用"; \
	fi

# 生成环境报告
report:
	@echo "生成环境报告..."
	@echo "=========================================="
	@echo "  多语言开发环境报告"
	@echo "=========================================="
	@echo "生成时间: $$(date)"
	@echo "主机名: $$(hostname)"
	@echo "操作系统: $$(uname -a)"
	@echo ""
	@make check-compilers
	@echo ""
	@make quick-test

# 帮助信息
help:
	@echo "多语言开发环境验证工具 Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all              - 编译所有验证程序"
	@echo "  test             - 运行完整验证测试"
	@echo "  test-python      - 仅运行Python验证"
	@echo "  test-go          - 仅运行Go验证"
	@echo "  test-cuda        - 仅运行CUDA验证"
	@echo "  quick-test       - 快速验证基本功能"
	@echo "  check-compilers  - 检查编译器可用性"
	@echo "  install-deps     - 安装验证工具依赖"
	@echo "  report           - 生成环境报告"
	@echo "  clean            - 清理编译产物"
	@echo "  help             - 显示此帮助信息"
	@echo ""
	@echo "使用示例:"
	@echo "  make all         # 编译所有程序"
	@echo "  make test        # 运行完整测试"
	@echo "  make quick-test  # 快速检查"
