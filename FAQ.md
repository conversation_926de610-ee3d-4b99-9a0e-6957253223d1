# 常见问题解决方案 (FAQ)

本文档收集了使用多语言机器学习开发环境时的常见问题和解决方案。

## 🔧 安装和配置问题

### Q1: Docker Desktop安装失败或无法启动

**问题描述**: Docker Desktop安装后无法启动，或提示WSL2相关错误。

**解决方案**:
1. **启用WSL2**:
   ```powershell
   # 以管理员身份运行PowerShell
   dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
   dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
   ```

2. **安装WSL2内核更新**:
   - 下载并安装 [WSL2 Linux内核更新包](https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi)

3. **设置WSL2为默认版本**:
   ```powershell
   wsl --set-default-version 2
   ```

4. **重启计算机**后再启动Docker Desktop

### Q2: NVIDIA Container Toolkit安装问题

**问题描述**: GPU在容器中不可用，nvidia-smi命令失败。

**解决方案**:
1. **检查NVIDIA驱动**:
   ```cmd
   nvidia-smi
   ```
   如果失败，请更新NVIDIA显卡驱动到最新版本。

2. **安装NVIDIA Container Toolkit**:
   ```powershell
   # 下载并安装NVIDIA Container Toolkit for Windows
   # 访问: https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html#docker
   ```

3. **重启Docker Desktop**

4. **验证GPU支持**:
   ```bash
   docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu22.04 nvidia-smi
   ```

### Q3: 构建过程中网络超时

**问题描述**: Docker构建时下载包超时或失败。

**解决方案**:
1. **配置Docker镜像源**:
   ```json
   // Docker Desktop设置 -> Docker Engine
   {
     "registry-mirrors": [
       "https://docker.mirrors.ustc.edu.cn",
       "https://hub-mirror.c.163.com"
     ]
   }
   ```

2. **使用代理**:
   ```bash
   # 在docker-compose.yml中添加
   build:
     context: .
     args:
       HTTP_PROXY: http://proxy:port
       HTTPS_PROXY: http://proxy:port
   ```

3. **分段构建**:
   ```bash
   # 如果完整构建失败，可以分段进行
   docker build --target base-stage .
   ```

## 🚀 运行时问题

### Q4: 容器启动后立即退出

**问题描述**: 容器启动后立即停止，状态显示为Exited。

**解决方案**:
1. **查看容器日志**:
   ```bash
   docker compose logs mldev
   ```

2. **检查内存限制**:
   ```yaml
   # 在docker-compose.yml中调整
   mem_limit: 16g  # 降低内存限制
   ```

3. **检查端口冲突**:
   ```bash
   netstat -an | findstr :8888
   # 如果端口被占用，修改docker-compose.yml中的端口映射
   ```

4. **以交互模式启动调试**:
   ```bash
   docker run -it --rm mldev:latest /bin/bash
   ```

### Q5: Jupyter Lab无法访问

**问题描述**: 浏览器无法打开http://localhost:8888。

**解决方案**:
1. **检查容器状态**:
   ```bash
   docker compose ps
   ```

2. **检查端口映射**:
   ```bash
   docker compose port mldev 8888
   ```

3. **手动启动Jupyter**:
   ```bash
   docker compose exec mldev jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --token=mldev2024
   ```

4. **检查防火墙设置**:
   - 确保Windows防火墙允许Docker Desktop访问网络

### Q6: GPU在PyTorch中不可用

**问题描述**: torch.cuda.is_available()返回False。

**解决方案**:
1. **验证容器GPU访问**:
   ```bash
   docker compose exec mldev nvidia-smi
   ```

2. **检查CUDA版本兼容性**:
   ```python
   import torch
   print(torch.version.cuda)  # 应该显示11.8
   ```

3. **重新安装PyTorch**:
   ```bash
   pip uninstall torch torchvision torchaudio
   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
   ```

4. **检查cuDNN**:
   ```python
   import torch
   print(torch.backends.cudnn.enabled)
   print(torch.backends.cudnn.version())
   ```

## 💾 存储和性能问题

### Q7: 容器内文件修改丢失

**问题描述**: 在容器内创建的文件在重启后消失。

**解决方案**:
1. **确保在挂载目录内工作**:
   ```bash
   # 工作目录应该在/workspace下
   cd /workspace
   ```

2. **检查卷挂载**:
   ```bash
   docker compose config
   # 确认volumes配置正确
   ```

3. **使用命名卷持久化数据**:
   ```yaml
   volumes:
     - persistent-data:/data
   ```

### Q8: 构建速度慢

**问题描述**: Docker镜像构建时间过长。

**解决方案**:
1. **使用构建缓存**:
   ```bash
   docker compose build --parallel
   ```

2. **多阶段构建优化**:
   ```dockerfile
   # 将不常变化的部分放在前面
   FROM nvidia/cuda:11.8-devel-ubuntu22.04 as base
   # 安装系统包...
   
   FROM base as python-env
   # 安装Python环境...
   ```

3. **使用.dockerignore**:
   ```
   # .dockerignore
   .git
   *.log
   node_modules
   __pycache__
   ```

### Q9: 内存使用过高

**问题描述**: 容器占用过多系统内存。

**解决方案**:
1. **调整内存限制**:
   ```yaml
   # docker-compose.yml
   mem_limit: 16g
   mem_reservation: 8g
   ```

2. **优化Python内存使用**:
   ```python
   import gc
   gc.collect()  # 手动垃圾回收
   
   # 使用生成器而不是列表
   data = (x for x in large_dataset)
   ```

3. **监控内存使用**:
   ```bash
   docker stats mldev-container
   ```

## 🔧 开发环境问题

### Q10: Go模块下载失败

**问题描述**: go get命令失败或超时。

**解决方案**:
1. **配置Go代理**:
   ```bash
   go env -w GOPROXY=https://goproxy.cn,direct
   go env -w GOSUMDB=sum.golang.google.cn
   ```

2. **清理模块缓存**:
   ```bash
   go clean -modcache
   ```

3. **使用vendor目录**:
   ```bash
   go mod vendor
   ```

### Q11: C++编译错误

**问题描述**: 编译C++程序时出现链接错误或找不到头文件。

**解决方案**:
1. **检查编译器版本**:
   ```bash
   gcc --version
   g++ --version
   ```

2. **安装缺失的开发库**:
   ```bash
   apt-get update
   apt-get install -y libboost-all-dev libeigen3-dev
   ```

3. **使用CMake管理依赖**:
   ```cmake
   find_package(Boost REQUIRED)
   target_link_libraries(your_target ${Boost_LIBRARIES})
   ```

### Q12: CUDA编译失败

**问题描述**: nvcc编译CUDA程序时出错。

**解决方案**:
1. **检查CUDA版本**:
   ```bash
   nvcc --version
   cat /usr/local/cuda/version.txt
   ```

2. **设置正确的架构**:
   ```bash
   nvcc -arch=sm_86 -o program program.cu  # RTX 4070 Super
   ```

3. **检查CUDA路径**:
   ```bash
   echo $CUDA_HOME
   echo $LD_LIBRARY_PATH
   ```

## 🌐 网络和连接问题

### Q13: 容器无法访问外网

**问题描述**: 容器内无法下载包或访问外部服务。

**解决方案**:
1. **检查DNS设置**:
   ```bash
   # 在容器内
   nslookup google.com
   cat /etc/resolv.conf
   ```

2. **配置Docker DNS**:
   ```json
   // Docker Desktop设置
   {
     "dns": ["*******", "*******"]
   }
   ```

3. **检查代理设置**:
   ```bash
   echo $HTTP_PROXY
   echo $HTTPS_PROXY
   ```

### Q14: 端口映射不工作

**问题描述**: 无法从主机访问容器内的服务。

**解决方案**:
1. **检查服务绑定地址**:
   ```python
   # 确保绑定到0.0.0.0而不是127.0.0.1
   app.run(host='0.0.0.0', port=8000)
   ```

2. **验证端口映射**:
   ```bash
   docker compose ps
   docker compose port mldev 8000
   ```

3. **检查防火墙**:
   ```powershell
   # Windows防火墙可能阻止连接
   netsh advfirewall firewall add rule name="Docker" dir=in action=allow protocol=TCP localport=8000
   ```

## 🔄 更新和维护问题

### Q15: 更新环境后出现兼容性问题

**问题描述**: 更新Docker镜像后，之前的代码无法运行。

**解决方案**:
1. **检查版本变化**:
   ```bash
   # 比较新旧环境的版本
   python --version
   pip list
   go version
   nvcc --version
   ```

2. **使用版本锁定**:
   ```txt
   # requirements.txt
   torch==2.1.0
   numpy==1.24.3
   ```

3. **创建迁移脚本**:
   ```python
   # migrate.py
   import subprocess
   import sys
   
   def upgrade_packages():
       packages = ['torch', 'numpy', 'pandas']
       for pkg in packages:
           subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', pkg])
   ```

### Q16: 清理Docker占用的磁盘空间

**问题描述**: Docker占用过多磁盘空间。

**解决方案**:
1. **清理未使用的镜像**:
   ```bash
   docker image prune -a
   ```

2. **清理未使用的卷**:
   ```bash
   docker volume prune
   ```

3. **完整清理**:
   ```bash
   docker system prune -a --volumes
   ```

4. **定期清理脚本**:
   ```bash
   # cleanup.bat
   docker compose down
   docker system prune -f
   docker volume prune -f
   ```

## 🛠️ 高级配置问题

### Q17: 自定义Python包安装

**问题描述**: 需要安装特定版本的包或私有包。

**解决方案**:
1. **修改Dockerfile**:
   ```dockerfile
   # 在Dockerfile中添加
   RUN pip install your-private-package==1.0.0
   ```

2. **使用requirements.txt**:
   ```bash
   # 在容器内
   pip install -r requirements.txt
   ```

3. **从源码安装**:
   ```bash
   pip install git+https://github.com/user/repo.git
   ```

### Q18: 配置开发工具

**问题描述**: 需要配置特定的编辑器或IDE设置。

**解决方案**:
1. **配置Vim**:
   ```bash
   # 在容器内创建.vimrc
   echo "set number" >> ~/.vimrc
   echo "syntax on" >> ~/.vimrc
   ```

2. **配置Git**:
   ```bash
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   ```

3. **安装额外工具**:
   ```dockerfile
   RUN apt-get update && apt-get install -y \
       tmux \
       screen \
       && rm -rf /var/lib/apt/lists/*
   ```

## 📞 获取更多帮助

如果以上解决方案都无法解决您的问题，请：

1. **运行诊断脚本**:
   ```bash
   python3 verify-environment.py
   make quick-test
   ```

2. **收集系统信息**:
   ```bash
   docker version
   docker compose version
   nvidia-smi
   systeminfo  # Windows
   ```

3. **查看详细日志**:
   ```bash
   docker compose logs --details mldev
   ```

4. **提交Issue时请包含**:
   - 操作系统版本
   - Docker版本
   - 错误日志
   - 复现步骤
   - 系统配置信息

---

**持续更新中...** 如果您遇到新的问题并找到了解决方案，欢迎贡献到这个FAQ！
