@echo off
REM 快速环境验证脚本 (Windows版本)
REM 用于在主机上检查Docker环境和基础配置

setlocal enabledelayedexpansion

echo.
echo ========================================
echo   快速环境验证 (主机端)
echo ========================================
echo.

REM 检查Docker
echo [检查] Docker环境...
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('docker --version') do echo [成功] Docker: %%i
) else (
    echo [错误] Docker未安装或未启动
    goto :error
)

docker compose version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('docker compose version') do echo [成功] Docker Compose: %%i
) else (
    echo [错误] Docker Compose不可用
    goto :error
)

REM 检查NVIDIA支持
echo.
echo [检查] NVIDIA支持...
nvidia-smi >nul 2>&1
if %errorlevel% equ 0 (
    echo [成功] NVIDIA驱动可用
    for /f "skip=9 tokens=2" %%i in ('nvidia-smi') do (
        echo [信息] GPU: %%i
        goto :gpu_found
    )
    :gpu_found
) else (
    echo [警告] NVIDIA驱动不可用或GPU不存在
)

REM 检查Docker GPU支持
echo.
echo [检查] Docker GPU支持...
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu22.04 nvidia-smi >nul 2>&1
if %errorlevel% equ 0 (
    echo [成功] Docker GPU支持可用
) else (
    echo [警告] Docker GPU支持不可用
)

REM 检查容器状态
echo.
echo [检查] 容器状态...
docker compose ps >nul 2>&1
if %errorlevel% equ 0 (
    echo [信息] Docker Compose项目状态:
    docker compose ps
) else (
    echo [信息] 容器未启动或项目不存在
)

REM 检查端口占用
echo.
echo [检查] 端口占用情况...
set ports=8888 8000 3000 8080 6006 5000

for %%p in (%ports%) do (
    netstat -an | findstr :%%p >nul 2>&1
    if !errorlevel! equ 0 (
        echo [信息] 端口 %%p 正在使用
    ) else (
        echo [信息] 端口 %%p 可用
    )
)

REM 检查磁盘空间
echo.
echo [检查] 磁盘空间...
for /f "tokens=3" %%i in ('dir /-c %SystemDrive%\ ^| findstr "bytes free"') do (
    set /a free_gb=%%i/1024/1024/1024
    echo [信息] 系统盘可用空间: !free_gb! GB
    if !free_gb! lss 10 (
        echo [警告] 磁盘空间不足，建议至少保留20GB
    )
)

REM 检查内存
echo.
echo [检查] 系统内存...
for /f "skip=1 tokens=2 delims==" %%i in ('wmic computersystem get TotalPhysicalMemory /value') do (
    if not "%%i"=="" (
        set /a total_gb=%%i/1024/1024/1024
        echo [信息] 总内存: !total_gb! GB
        if !total_gb! lss 16 (
            echo [警告] 内存可能不足，推荐32GB以上
        )
    )
)

REM 检查工作目录
echo.
echo [检查] 工作目录...
echo [信息] 当前目录: %CD%
if exist "Dockerfile" (
    echo [成功] 找到 Dockerfile
) else (
    echo [警告] 未找到 Dockerfile
)

if exist "docker-compose.yml" (
    echo [成功] 找到 docker-compose.yml
) else (
    echo [警告] 未找到 docker-compose.yml
)

if exist "start-mldev.bat" (
    echo [成功] 找到启动脚本
) else (
    echo [警告] 未找到启动脚本
)

REM 如果容器正在运行，检查容器内环境
echo.
echo [检查] 容器内环境...
docker compose exec -T mldev echo "容器连接成功" >nul 2>&1
if %errorlevel% equ 0 (
    echo [成功] 容器正在运行，检查内部环境...
    
    REM 检查Python
    docker compose exec -T mldev python3 --version 2>nul
    if !errorlevel! equ 0 (
        for /f "tokens=*" %%i in ('docker compose exec -T mldev python3 --version 2^>nul') do echo [成功] 容器内Python: %%i
    ) else (
        echo [错误] 容器内Python不可用
    )
    
    REM 检查PyTorch
    docker compose exec -T mldev python3 -c "import torch; print('PyTorch:', torch.__version__)" 2>nul
    if !errorlevel! equ 0 (
        for /f "tokens=*" %%i in ('docker compose exec -T mldev python3 -c "import torch; print('PyTorch:', torch.__version__)" 2^>nul') do echo [成功] %%i
    ) else (
        echo [错误] 容器内PyTorch不可用
    )
    
    REM 检查CUDA
    docker compose exec -T mldev python3 -c "import torch; print('CUDA available:', torch.cuda.is_available())" 2>nul
    if !errorlevel! equ 0 (
        for /f "tokens=*" %%i in ('docker compose exec -T mldev python3 -c "import torch; print('CUDA available:', torch.cuda.is_available())" 2^>nul') do echo [信息] %%i
    )
    
    REM 检查Go
    docker compose exec -T mldev go version 2>nul
    if !errorlevel! equ 0 (
        for /f "tokens=*" %%i in ('docker compose exec -T mldev go version 2^>nul') do echo [成功] 容器内Go: %%i
    ) else (
        echo [错误] 容器内Go不可用
    )
    
    REM 检查NVCC
    docker compose exec -T mldev nvcc --version 2>nul | findstr "release" >nul
    if !errorlevel! equ 0 (
        for /f "tokens=*" %%i in ('docker compose exec -T mldev nvcc --version 2^>nul ^| findstr "release"') do echo [成功] 容器内NVCC: %%i
    ) else (
        echo [错误] 容器内NVCC不可用
    )
    
) else (
    echo [信息] 容器未运行，跳过容器内检查
    echo [提示] 使用以下命令启动容器:
    echo         start-mldev.bat
    echo         或 docker compose up -d
)

REM 总结
echo.
echo ========================================
echo   验证完成
echo ========================================
echo.
echo [信息] 快速验证已完成
echo [提示] 如需详细验证，请:
echo         1. 启动容器: start-mldev.bat
echo         2. 进入容器: docker compose exec mldev /bin/zsh
echo         3. 运行验证: python3 verify-environment.py
echo.

REM 检查是否有严重错误
if exist error_flag (
    echo [警告] 发现一些问题，请检查上述输出
    del error_flag
    pause
    exit /b 1
) else (
    echo [成功] 基础环境检查通过
    pause
    exit /b 0
)

:error
echo [错误] 环境检查失败，请安装必要的软件
echo > error_flag
pause
exit /b 1
