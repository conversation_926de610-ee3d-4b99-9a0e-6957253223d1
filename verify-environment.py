#!/usr/bin/env python3
"""
多语言机器学习开发环境验证脚本
验证Python、CUDA、PyTorch等环境是否正确配置
"""

import sys
import subprocess
import importlib
import platform
from pathlib import Path
from typing import Dict, List, Tuple, Optional

class Colors:
    """终端颜色常量"""
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_colored(message: str, color: str = Colors.WHITE) -> None:
    """打印彩色文本"""
    print(f"{color}{message}{Colors.END}")

def print_header(title: str) -> None:
    """打印标题"""
    print_colored(f"\n{'='*60}", Colors.MAGENTA)
    print_colored(f"  {title}", Colors.MAGENTA + Colors.BOLD)
    print_colored(f"{'='*60}", Colors.MAGENTA)

def print_success(message: str) -> None:
    """打印成功信息"""
    print_colored(f"✓ {message}", Colors.GREEN)

def print_error(message: str) -> None:
    """打印错误信息"""
    print_colored(f"✗ {message}", Colors.RED)

def print_warning(message: str) -> None:
    """打印警告信息"""
    print_colored(f"⚠ {message}", Colors.YELLOW)

def print_info(message: str) -> None:
    """打印信息"""
    print_colored(f"ℹ {message}", Colors.CYAN)

def check_python_environment() -> Dict[str, bool]:
    """检查Python环境"""
    print_header("Python环境检查")
    results = {}
    
    # Python版本
    python_version = sys.version_info
    print_info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    results['python_version'] = python_version >= (3, 8)
    
    if results['python_version']:
        print_success("Python版本符合要求 (>= 3.8)")
    else:
        print_error("Python版本过低，建议使用3.8+")
    
    # 检查重要的Python包
    packages = [
        'numpy', 'pandas', 'matplotlib', 'seaborn', 'plotly',
        'scikit-learn', 'jupyter', 'jupyterlab',
        'requests', 'beautifulsoup4', 'pillow', 'opencv-python',
        'black', 'flake8', 'pytest', 'mypy'
    ]
    
    for package in packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            print_success(f"{package} 已安装")
            results[f'package_{package}'] = True
        except ImportError:
            print_error(f"{package} 未安装")
            results[f'package_{package}'] = False
    
    return results

def check_pytorch_environment() -> Dict[str, bool]:
    """检查PyTorch环境"""
    print_header("PyTorch环境检查")
    results = {}
    
    try:
        import torch
        import torchvision
        import torchaudio
        
        print_success("PyTorch生态系统已安装")
        print_info(f"PyTorch版本: {torch.__version__}")
        print_info(f"TorchVision版本: {torchvision.__version__}")
        print_info(f"TorchAudio版本: {torchaudio.__version__}")
        
        results['pytorch_installed'] = True
        
        # 检查CUDA支持
        cuda_available = torch.cuda.is_available()
        results['cuda_available'] = cuda_available
        
        if cuda_available:
            print_success("CUDA支持可用")
            print_info(f"CUDA版本: {torch.version.cuda}")
            print_info(f"cuDNN版本: {torch.backends.cudnn.version()}")
            print_info(f"GPU数量: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print_info(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        else:
            print_warning("CUDA支持不可用，将使用CPU模式")
        
        # 简单的张量操作测试
        try:
            x = torch.randn(3, 3)
            y = torch.randn(3, 3)
            z = torch.mm(x, y)
            print_success("CPU张量操作测试通过")
            results['cpu_tensor_ops'] = True
            
            if cuda_available:
                x_gpu = x.cuda()
                y_gpu = y.cuda()
                z_gpu = torch.mm(x_gpu, y_gpu)
                print_success("GPU张量操作测试通过")
                results['gpu_tensor_ops'] = True
            else:
                results['gpu_tensor_ops'] = False
                
        except Exception as e:
            print_error(f"张量操作测试失败: {e}")
            results['cpu_tensor_ops'] = False
            results['gpu_tensor_ops'] = False
            
    except ImportError as e:
        print_error(f"PyTorch未安装: {e}")
        results['pytorch_installed'] = False
        results['cuda_available'] = False
        results['cpu_tensor_ops'] = False
        results['gpu_tensor_ops'] = False
    
    return results

def check_cuda_environment() -> Dict[str, bool]:
    """检查CUDA环境"""
    print_header("CUDA环境检查")
    results = {}
    
    # 检查nvidia-smi
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success("nvidia-smi 可用")
            results['nvidia_smi'] = True
            
            # 解析GPU信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version' in line:
                    cuda_version = line.split('CUDA Version: ')[1].split()[0]
                    print_info(f"NVIDIA驱动CUDA版本: {cuda_version}")
                    break
        else:
            print_error("nvidia-smi 不可用")
            results['nvidia_smi'] = False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print_error("nvidia-smi 命令未找到")
        results['nvidia_smi'] = False
    
    # 检查CUDA编译器
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print_success("NVCC编译器可用")
            results['nvcc'] = True
            
            # 解析NVCC版本
            lines = result.stdout.split('\n')
            for line in lines:
                if 'release' in line:
                    version_info = line.strip()
                    print_info(f"NVCC版本: {version_info}")
                    break
        else:
            print_error("NVCC编译器不可用")
            results['nvcc'] = False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print_error("NVCC命令未找到")
        results['nvcc'] = False
    
    return results

def check_cpp_environment() -> Dict[str, bool]:
    """检查C++环境"""
    print_header("C++环境检查")
    results = {}
    
    # 检查GCC/G++
    compilers = ['gcc', 'g++']
    for compiler in compilers:
        try:
            result = subprocess.run([compiler, '--version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print_success(f"{compiler.upper()} 可用: {version_line}")
                results[compiler] = True
            else:
                print_error(f"{compiler.upper()} 不可用")
                results[compiler] = False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print_error(f"{compiler.upper()} 命令未找到")
            results[compiler] = False
    
    # 检查CMake
    try:
        result = subprocess.run(['cmake', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print_success(f"CMake 可用: {version_line}")
            results['cmake'] = True
        else:
            print_error("CMake 不可用")
            results['cmake'] = False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print_error("CMake 命令未找到")
        results['cmake'] = False
    
    return results

def check_go_environment() -> Dict[str, bool]:
    """检查Go环境"""
    print_header("Go环境检查")
    results = {}
    
    try:
        result = subprocess.run(['go', 'version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_info = result.stdout.strip()
            print_success(f"Go 可用: {version_info}")
            results['go'] = True
            
            # 检查Go环境变量
            env_result = subprocess.run(['go', 'env'], capture_output=True, text=True, timeout=10)
            if env_result.returncode == 0:
                env_lines = env_result.stdout.split('\n')
                for line in env_lines:
                    if line.startswith('GOROOT='):
                        print_info(f"GOROOT: {line.split('=')[1].strip('\"')}")
                    elif line.startswith('GOPATH='):
                        print_info(f"GOPATH: {line.split('=')[1].strip('\"')}")
        else:
            print_error("Go 不可用")
            results['go'] = False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print_error("Go 命令未找到")
        results['go'] = False
    
    return results

def run_performance_test() -> Dict[str, bool]:
    """运行性能测试"""
    print_header("性能测试")
    results = {}
    
    try:
        import torch
        import time
        
        # CPU性能测试
        print_info("运行CPU性能测试...")
        start_time = time.time()
        x = torch.randn(1000, 1000)
        y = torch.randn(1000, 1000)
        for _ in range(10):
            z = torch.mm(x, y)
        cpu_time = time.time() - start_time
        print_success(f"CPU矩阵乘法测试完成: {cpu_time:.2f}秒")
        results['cpu_performance'] = True
        
        # GPU性能测试
        if torch.cuda.is_available():
            print_info("运行GPU性能测试...")
            x_gpu = torch.randn(1000, 1000).cuda()
            y_gpu = torch.randn(1000, 1000).cuda()
            
            # 预热
            for _ in range(5):
                torch.mm(x_gpu, y_gpu)
            torch.cuda.synchronize()
            
            start_time = time.time()
            for _ in range(10):
                z_gpu = torch.mm(x_gpu, y_gpu)
            torch.cuda.synchronize()
            gpu_time = time.time() - start_time
            
            print_success(f"GPU矩阵乘法测试完成: {gpu_time:.2f}秒")
            print_info(f"GPU加速比: {cpu_time/gpu_time:.1f}x")
            results['gpu_performance'] = True
        else:
            print_warning("GPU不可用，跳过GPU性能测试")
            results['gpu_performance'] = False
            
    except Exception as e:
        print_error(f"性能测试失败: {e}")
        results['cpu_performance'] = False
        results['gpu_performance'] = False
    
    return results

def generate_report(all_results: Dict[str, Dict[str, bool]]) -> None:
    """生成测试报告"""
    print_header("环境验证报告")
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        print_colored(f"\n{category}:", Colors.BOLD)
        for test_name, result in results.items():
            total_tests += 1
            if result:
                passed_tests += 1
                print_success(f"  {test_name}")
            else:
                print_error(f"  {test_name}")
    
    print_colored(f"\n总体结果:", Colors.BOLD)
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    print_info(f"通过测试: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print_success("环境配置良好！")
    elif success_rate >= 60:
        print_warning("环境基本可用，但有一些问题需要解决")
    else:
        print_error("环境配置存在较多问题，建议重新配置")

def main():
    """主函数"""
    print_colored("多语言机器学习开发环境验证工具", Colors.MAGENTA + Colors.BOLD)
    print_colored("="*60, Colors.MAGENTA)
    
    # 系统信息
    print_info(f"操作系统: {platform.system()} {platform.release()}")
    print_info(f"Python解释器: {sys.executable}")
    print_info(f"工作目录: {Path.cwd()}")
    
    # 运行所有检查
    all_results = {}
    all_results['Python环境'] = check_python_environment()
    all_results['PyTorch环境'] = check_pytorch_environment()
    all_results['CUDA环境'] = check_cuda_environment()
    all_results['C++环境'] = check_cpp_environment()
    all_results['Go环境'] = check_go_environment()
    all_results['性能测试'] = run_performance_test()
    
    # 生成报告
    generate_report(all_results)

if __name__ == "__main__":
    main()
