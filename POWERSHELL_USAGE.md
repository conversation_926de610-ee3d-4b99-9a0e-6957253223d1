# PowerShell启动脚本使用说明

## 🔧 修复完成

PowerShell脚本 `start-mldev.ps1` 中的所有语法错误已经修复：

### ✅ 已修复的问题
1. **Try语句缺少Catch块** - 已为所有try语句添加对应的catch块
2. **缺少闭合括号** - 已修复所有括号配对问题
3. **字符串缺少终止符** - 已修复所有字符串引号问题
4. **函数定义缺少闭合大括号** - 已为所有函数添加正确的大括号
5. **中文字符编码问题** - 已确保所有中文字符正确编码

### ✅ 语法验证
- PowerShell解析器验证通过
- 所有函数定义正确
- 参数处理正常
- 错误处理完善

## 🚀 使用方法

### 1. 基本运行
```powershell
# 显示交互式菜单
.\start-mldev.ps1

# 或者
powershell -ExecutionPolicy Bypass -File "start-mldev.ps1"
```

### 2. 直接执行特定操作
```powershell
# 构建并启动环境
.\start-mldev.ps1 -Action build

# 启动已有环境
.\start-mldev.ps1 -Action start

# 停止环境
.\start-mldev.ps1 -Action stop

# 查看状态
.\start-mldev.ps1 -Action status

# 进入容器Shell
.\start-mldev.ps1 -Action shell

# 启动Jupyter Lab
.\start-mldev.ps1 -Action jupyter

# 查看日志
.\start-mldev.ps1 -Action logs

# 清理环境
.\start-mldev.ps1 -Action clean

# 重建环境
.\start-mldev.ps1 -Action rebuild
```

### 3. 使用参数选项
```powershell
# 跳过GPU检查
.\start-mldev.ps1 -NoGPU

# 强制重建
.\start-mldev.ps1 -Rebuild

# 详细输出
.\start-mldev.ps1 -Verbose

# 组合使用
.\start-mldev.ps1 -Action build -NoGPU -Verbose
```

## 🛠️ 执行策略设置

如果遇到执行策略限制，请使用以下方法之一：

### 方法1：临时绕过执行策略
```powershell
powershell -ExecutionPolicy Bypass -File "start-mldev.ps1"
```

### 方法2：为当前用户设置执行策略
```powershell
# 以管理员身份运行PowerShell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 方法3：解除文件阻止
```powershell
# 如果文件被Windows阻止
Unblock-File -Path "start-mldev.ps1"
```

## 📋 功能说明

### 主要功能
- **环境检查**: 自动检查Docker、Docker Compose和NVIDIA支持
- **容器管理**: 构建、启动、停止、重建容器
- **开发工具**: 快速进入容器、启动Jupyter Lab
- **状态监控**: 查看容器状态和日志
- **环境清理**: 清理容器、镜像和卷

### 交互式菜单
运行脚本后会显示以下菜单：
```
请选择操作:
1. 构建并启动开发环境
2. 启动已有环境
3. 停止环境
4. 重建环境（清除缓存）
5. 查看环境状态
6. 进入容器Shell
7. 启动Jupyter Lab
8. 查看日志
9. 清理环境
0. 退出
```

## 🔍 故障排除

### 常见问题

#### 1. 执行策略错误
```
无法加载文件，因为在此系统上禁止运行脚本
```
**解决方案**: 使用 `-ExecutionPolicy Bypass` 参数

#### 2. Docker未启动
```
Docker未安装或未启动
```
**解决方案**: 启动Docker Desktop

#### 3. NVIDIA支持不可用
```
NVIDIA Docker支持不可用，将以CPU模式运行
```
**解决方案**: 安装NVIDIA Container Toolkit或使用 `-NoGPU` 参数

#### 4. 端口冲突
```
端口已被占用
```
**解决方案**: 修改 `docker-compose.yml` 中的端口映射

### 调试模式
```powershell
# 启用详细输出
.\start-mldev.ps1 -Verbose

# 查看详细错误信息
$ErrorActionPreference = "Stop"
.\start-mldev.ps1
```

## 📝 脚本特性

### 安全特性
- 错误处理机制
- 参数验证
- 执行前检查
- 用户确认提示

### 用户体验
- 彩色输出
- 进度提示
- 清晰的错误信息
- 交互式菜单

### 兼容性
- Windows 10/11
- PowerShell 5.1+
- PowerShell Core 7+
- 支持中文字符

## 🎯 快速开始

1. **确保Docker运行**:
   ```powershell
   docker --version
   ```

2. **运行脚本**:
   ```powershell
   .\start-mldev.ps1
   ```

3. **选择操作**:
   - 首次使用选择 "1. 构建并启动开发环境"
   - 后续使用选择 "2. 启动已有环境"

4. **进入开发环境**:
   ```powershell
   .\start-mldev.ps1 -Action shell
   ```

5. **启动Jupyter Lab**:
   ```powershell
   .\start-mldev.ps1 -Action jupyter
   ```

## 📞 获取帮助

如果仍然遇到问题：

1. **查看详细日志**:
   ```powershell
   .\start-mldev.ps1 -Action logs
   ```

2. **运行诊断**:
   ```powershell
   .\quick-verify.bat
   ```

3. **查看FAQ**:
   参考 `FAQ.md` 文件

4. **重置环境**:
   ```powershell
   .\start-mldev.ps1 -Action clean
   .\start-mldev.ps1 -Action build
   ```

---

**PowerShell脚本现在已经完全修复并可以正常使用！** 🎉
